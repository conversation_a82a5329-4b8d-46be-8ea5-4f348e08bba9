import { MenuDataItem } from "@ant-design/pro-components"

export type { ThemeMode } from "antd-style"

export type Local = "fr" | "en"

export interface Menus extends MenuDataItem {
  labels: {
    fr: string
    en: string
  },
  children?: Menus[]
  roles?: string[]
  permissions?: string[]
  parentPath?: string
}

export interface AppResponse<T = any> {
  timestamp: string;
  status: string;
  error?: string;
  message?: string;
  data?: T[];
}

export interface User {
  email: string;
  role: string;
  permissions: string[];
}

export interface AuthState {
  token: string;
  isAuthenticated: boolean;
  user?: User;
}

export interface LoginPayload {
    email: string
    password: string
    remember: boolean
}
